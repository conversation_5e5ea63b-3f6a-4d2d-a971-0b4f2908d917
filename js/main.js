// Digistore3 - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initThemeToggle();
    initMobileNavigation();
    initStickyNavigation();
    initCurrencySelector();
    initLanguageToggle();
    initMegaMenus();
    initScrollAnimations();
    initSearchFunctionality();
    initCartWishlistUpdates();
});

// Theme Toggle Functionality
function initThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    const body = document.body;
    
    // Check for saved theme preference or default to 'dark'
    const currentTheme = localStorage.getItem('theme') || 'dark';
    body.setAttribute('data-theme', currentTheme);
    
    themeToggle.addEventListener('click', function() {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        // Add animation class
        themeToggle.classList.add('rotating');
        setTimeout(() => themeToggle.classList.remove('rotating'), 300);
    });
}

// Mobile Navigation
function initMobileNavigation() {
    const mobileBar = document.getElementById('mobileBar');
    const mobileBtns = mobileBar.querySelectorAll('.mobile-btn');
    
    mobileBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            mobileBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Handle different actions
            const action = this.getAttribute('data-action');
            handleMobileAction(action);
        });
    });
    
    // Auto-hide mobile bar on scroll down, show on scroll up
    let lastScrollTop = 0;
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (window.innerWidth <= 768) {
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // Scrolling down
                mobileBar.classList.add('slide-up');
            } else {
                // Scrolling up
                mobileBar.classList.remove('slide-up');
            }
        }
        
        lastScrollTop = scrollTop;
    });
}

// Handle mobile navigation actions
function handleMobileAction(action) {
    switch(action) {
        case 'home':
            window.location.href = '#';
            break;
        case 'categories':
            // Toggle categories menu or navigate
            console.log('Categories clicked');
            break;
        case 'search':
            toggleSearch();
            break;
        case 'wishlist':
            window.location.href = '#wishlist';
            break;
        case 'cart':
            window.location.href = '#cart';
            break;
    }
}

// Sticky Navigation
function initStickyNavigation() {
    const primaryNav = document.getElementById('primaryNav');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            primaryNav.classList.add('scrolled');
        } else {
            primaryNav.classList.remove('scrolled');
        }
    });
}

// Currency Selector
function initCurrencySelector() {
    const currencyBtns = document.querySelectorAll('[data-currency]');
    
    currencyBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const currency = this.getAttribute('data-currency');
            updateCurrency(currency);
            
            // Update active state
            currencyBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Update currency display
function updateCurrency(currency) {
    const currencyMap = {
        'usd': { symbol: '$', rate: 1 },
        'eur': { symbol: '€', rate: 0.85 },
        'sar': { symbol: 'ر.س', rate: 3.75 }
    };
    
    // Store selected currency
    localStorage.setItem('selectedCurrency', currency);
    
    // Update all price displays
    const priceElements = document.querySelectorAll('.price');
    priceElements.forEach(element => {
        const basePrice = parseFloat(element.getAttribute('data-base-price') || element.textContent.replace(/[^\d.]/g, ''));
        const convertedPrice = (basePrice * currencyMap[currency].rate).toFixed(2);
        element.textContent = `${currencyMap[currency].symbol}${convertedPrice}`;
    });
    
    console.log(`Currency changed to: ${currency.toUpperCase()}`);
}

// Language Toggle
function initLanguageToggle() {
    const langBtns = document.querySelectorAll('.lang-btn');
    
    langBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const lang = this.getAttribute('data-lang');
            updateLanguage(lang);
            
            // Update active state
            langBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Update language and RTL support
function updateLanguage(lang) {
    const html = document.documentElement;
    
    if (lang === 'ar') {
        html.setAttribute('dir', 'rtl');
        html.setAttribute('lang', 'ar');
    } else {
        html.setAttribute('dir', 'ltr');
        html.setAttribute('lang', 'en');
    }
    
    localStorage.setItem('selectedLanguage', lang);
    console.log(`Language changed to: ${lang}`);
}

// Mega Menu Functionality
function initMegaMenus() {
    const megaMenuItems = document.querySelectorAll('.has-mega');
    
    megaMenuItems.forEach(item => {
        const megaMenu = item.querySelector('.mega-menu');
        let hoverTimeout;
        
        item.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
            megaMenu.classList.remove('closing');
        });
        
        item.addEventListener('mouseleave', function() {
            megaMenu.classList.add('closing');
            hoverTimeout = setTimeout(() => {
                megaMenu.classList.remove('closing');
            }, 200);
        });
    });
}

// Scroll Animations using Intersection Observer
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('in-view');
            }
        });
    }, observerOptions);
    
    // Observe elements with animation classes
    const animatedElements = document.querySelectorAll('.animate-on-scroll, .fade-in-up, .fade-in-left, .fade-in-right');
    animatedElements.forEach(el => observer.observe(el));
}

// Search Functionality
function initSearchFunctionality() {
    const searchBtns = document.querySelectorAll('.search-btn');
    
    searchBtns.forEach(btn => {
        btn.addEventListener('click', toggleSearch);
    });
}

function toggleSearch() {
    // Create or toggle search overlay
    let searchOverlay = document.getElementById('searchOverlay');
    
    if (!searchOverlay) {
        searchOverlay = createSearchOverlay();
        document.body.appendChild(searchOverlay);
    }
    
    searchOverlay.classList.toggle('active');
    
    if (searchOverlay.classList.contains('active')) {
        const searchInput = searchOverlay.querySelector('input');
        setTimeout(() => searchInput.focus(), 100);
    }
}

function createSearchOverlay() {
    const overlay = document.createElement('div');
    overlay.id = 'searchOverlay';
    overlay.className = 'search-overlay';
    overlay.innerHTML = `
        <div class="search-container">
            <div class="search-header">
                <input type="text" placeholder="Search products, categories..." class="search-input">
                <button class="search-close">&times;</button>
            </div>
            <div class="search-results">
                <div class="search-suggestions">
                    <h4>Popular Searches</h4>
                    <div class="suggestion-tags">
                        <span class="tag">Business Templates</span>
                        <span class="tag">Kids Games</span>
                        <span class="tag">Printables</span>
                        <span class="tag">Social Media</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add event listeners
    const closeBtn = overlay.querySelector('.search-close');
    closeBtn.addEventListener('click', () => overlay.classList.remove('active'));
    
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            overlay.classList.remove('active');
        }
    });
    
    return overlay;
}

// Cart and Wishlist Updates
function initCartWishlistUpdates() {
    // Simulate badge updates
    setTimeout(() => {
        updateBadge('.cart-btn .badge', 3);
        updateBadge('.wishlist-btn .badge', 5);
    }, 2000);
}

function updateBadge(selector, count) {
    const badge = document.querySelector(selector);
    if (badge) {
        badge.textContent = count;
        badge.classList.add('badge-update');
        setTimeout(() => badge.classList.remove('badge-update'), 500);
    }
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance optimization: Lazy load images
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', initLazyLoading);

// Handle window resize
window.addEventListener('resize', debounce(function() {
    // Recalculate any size-dependent elements
    console.log('Window resized');
}, 250));

// Export functions for external use
window.DigiStore3 = {
    updateCurrency,
    updateLanguage,
    toggleSearch,
    updateBadge
};
