// Digistore3 Configuration Example
// Copy this file to config.js and customize as needed

window.DigiStore3Config = {
  // Site Information
  site: {
    name: "Digistore3",
    tagline: "Premium Digital Products & Templates",
    description: "Discover premium digital products, templates, educational games, and printables for kids and professionals.",
    url: "https://your-domain.com",
    email: "<EMAIL>",
    phone: "+****************"
  },

  // Theme Configuration
  theme: {
    defaultTheme: "dark", // "dark" or "light"
    accentColor: "#ff6f61",
    accentHover: "#ff5a4a",
    enableAnimations: true,
    enableParticles: true
  },

  // Language & Localization
  localization: {
    defaultLanguage: "en",
    supportedLanguages: ["en", "ar"],
    defaultCurrency: "usd",
    supportedCurrencies: {
      usd: { symbol: "$", rate: 1, name: "US Dollar" },
      eur: { symbol: "€", rate: 0.85, name: "Euro" },
      sar: { symbol: "ر.س", rate: 3.75, name: "Saudi Riyal" }
    }
  },

  // Navigation Menu
  navigation: {
    mainMenu: [
      { name: "Home", url: "#", active: true },
      { 
        name: "Categories", 
        url: "#", 
        megaMenu: {
          type: "categories",
          columns: 4,
          showFeatured: true
        }
      },
      { 
        name: "Resources", 
        url: "#", 
        megaMenu: {
          type: "resources",
          columns: 3,
          showFooter: true
        }
      },
      { name: "Kids Corner", url: "#" },
      { name: "Blog", url: "#" },
      { name: "Shop", url: "#" }
    ]
  },

  // Product Categories
  categories: [
    {
      id: "business",
      name: "Business Templates",
      description: "Professional presentations, reports, and business documents",
      itemCount: 150,
      icon: "business",
      subcategories: [
        { name: "Business Cards", url: "#", hot: true },
        { name: "Presentations", url: "#" },
        { name: "Social Media", url: "#" },
        { name: "Logos & Branding", url: "#" }
      ]
    },
    {
      id: "education",
      name: "Educational",
      description: "Learning games, activities, and educational materials",
      itemCount: 200,
      icon: "education",
      subcategories: [
        { name: "Kids Games", url: "#" },
        { name: "Learning Materials", url: "#" },
        { name: "Worksheets", url: "#" },
        { name: "Activity Books", url: "#" }
      ]
    },
    {
      id: "printables",
      name: "Printables",
      description: "Planners, wall art, calendars, and printable designs",
      itemCount: 300,
      icon: "printables",
      subcategories: [
        { name: "Planners", url: "#" },
        { name: "Wall Art", url: "#" },
        { name: "Calendars", url: "#" },
        { name: "Stickers", url: "#" }
      ]
    },
    {
      id: "digital-assets",
      name: "Digital Assets",
      description: "Icons, graphics, fonts, and design elements",
      itemCount: 500,
      icon: "digital",
      subcategories: [
        { name: "Stock Photos", url: "#" },
        { name: "Icons & Graphics", url: "#" },
        { name: "Fonts", url: "#" },
        { name: "Mockups", url: "#" }
      ]
    }
  ],

  // Featured Products
  featuredProducts: [
    {
      id: 1,
      title: "Premium Business Template",
      description: "Professional presentation template for modern businesses",
      price: 29.99,
      image: "assets/products/featured-1.jpg",
      rating: 5,
      reviewCount: 127,
      tags: ["business", "presentation", "professional"]
    },
    {
      id: 2,
      title: "Educational Kids Activity Pack",
      description: "Fun learning activities for children ages 5-10",
      price: 19.99,
      image: "assets/products/featured-2.jpg",
      rating: 4,
      reviewCount: 89,
      tags: ["kids", "education", "activities"]
    }
  ],

  // Hero Section
  hero: {
    title: "Premium Digital Products for Creative Professionals",
    subtitle: "Discover thousands of high-quality templates, educational games, and digital assets to boost your creativity and productivity.",
    primaryCTA: {
      text: "Explore Products",
      url: "#products"
    },
    secondaryCTA: {
      text: "Watch Demo",
      url: "#demo"
    },
    stats: [
      { number: "10k+", label: "Downloads" },
      { number: "4.8★", label: "Ratings" },
      { number: "98%", label: "Satisfaction" }
    ]
  },

  // Social Media Links
  social: {
    facebook: "https://facebook.com/digistore3",
    twitter: "https://twitter.com/digistore3",
    instagram: "https://instagram.com/digistore3",
    linkedin: "https://linkedin.com/company/digistore3"
  },

  // Google AdSense Configuration
  adsense: {
    enabled: false, // Set to true when ready to show ads
    publisherId: "ca-pub-XXXXXXXXXX", // Replace with your AdSense publisher ID
    adUnits: {
      topBanner: {
        slot: "1234567890",
        format: "auto",
        responsive: true
      },
      sectionBreak: {
        slot: "0987654321",
        format: "rectangle",
        responsive: true
      },
      footer: {
        slot: "1122334455",
        format: "horizontal",
        responsive: true
      }
    }
  },

  // Analytics
  analytics: {
    googleAnalytics: {
      enabled: false,
      trackingId: "GA-XXXXXXXXX-X"
    },
    facebookPixel: {
      enabled: false,
      pixelId: "XXXXXXXXXXXXXXXXX"
    }
  },

  // Performance Settings
  performance: {
    lazyLoadImages: true,
    enableServiceWorker: false,
    preloadCriticalResources: true,
    optimizeAnimations: true
  },

  // Contact Information
  contact: {
    email: "<EMAIL>",
    phone: "+****************",
    address: {
      street: "123 Digital Street",
      city: "Tech City",
      state: "CA",
      zip: "12345",
      country: "USA"
    },
    supportHours: "Mon-Fri 9AM-6PM PST"
  },

  // Footer Links
  footer: {
    sections: [
      {
        title: "Products",
        links: [
          { name: "Business Templates", url: "#" },
          { name: "Educational Games", url: "#" },
          { name: "Printables", url: "#" },
          { name: "Digital Assets", url: "#" }
        ]
      },
      {
        title: "Support",
        links: [
          { name: "Help Center", url: "#" },
          { name: "Contact Us", url: "#" },
          { name: "Downloads", url: "#" },
          { name: "Refund Policy", url: "#" }
        ]
      },
      {
        title: "Company",
        links: [
          { name: "About Us", url: "#" },
          { name: "Blog", url: "#" },
          { name: "Careers", url: "#" },
          { name: "Press", url: "#" }
        ]
      }
    ],
    legal: [
      { name: "Privacy Policy", url: "#" },
      { name: "Terms of Service", url: "#" },
      { name: "Cookie Policy", url: "#" }
    ],
    copyright: "© 2024 Digistore3. All rights reserved."
  }
};

// Initialize configuration when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  if (window.DigiStore3 && window.DigiStore3Config) {
    // Apply configuration to the site
    console.log('Digistore3 configuration loaded');
    
    // You can add initialization code here to apply the configuration
    // For example, updating site title, contact info, etc.
  }
});
