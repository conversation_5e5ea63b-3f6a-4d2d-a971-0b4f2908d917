<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="productGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6f61;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8a65;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="200" fill="url(#productGrad1)"/>
  <rect x="20" y="20" width="160" height="160" fill="white" opacity="0.9" rx="10"/>
  <text x="100" y="80" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Business</text>
  <text x="100" y="100" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Template</text>
  <text x="100" y="130" text-anchor="middle" fill="#666" font-size="12">Premium Design</text>
  <circle cx="100" cy="150" r="15" fill="#ff6f61"/>
  <text x="100" y="155" text-anchor="middle" fill="white" font-size="10">★</text>
</svg>
