<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="productGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4caf50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66bb6a;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="200" fill="url(#productGrad2)"/>
  <rect x="20" y="20" width="160" height="160" fill="white" opacity="0.9" rx="10"/>
  <text x="100" y="80" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Kids</text>
  <text x="100" y="100" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Activity</text>
  <text x="100" y="130" text-anchor="middle" fill="#666" font-size="12">Educational Fun</text>
  <circle cx="100" cy="150" r="15" fill="#4caf50"/>
  <text x="100" y="155" text-anchor="middle" fill="white" font-size="10">🎮</text>
</svg>
