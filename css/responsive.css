/* Digistore3 - Responsive Styles */

/* Tablet Styles */
@media (max-width: 1024px) {
  :root {
    --container-padding: 16px;
  }
  
  .container {
    padding: 0 var(--container-padding);
  }
  
  /* Navigation adjustments */
  .main-menu {
    gap: 24px;
  }
  
  .nav-right {
    gap: 12px;
  }
  
  /* Mega menu adjustments */
  .mega-menu {
    min-width: 700px;
    padding: 24px;
  }
  
  .categories-grid {
    gap: 24px;
  }
  
  .resources-grid {
    gap: 16px;
  }
  
  /* Hero adjustments */
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-stats {
    gap: 24px;
  }
}

/* Mobile Landscape */
@media (max-width: 768px) {
  :root {
    --container-padding: 12px;
    --nav-height: 60px;
  }
  
  /* Hide desktop navigation elements */
  .utility-bar {
    display: none;
  }
  
  .nav-center {
    display: none;
  }
  
  .nav-right {
    gap: 8px;
  }
  
  /* Adjust primary nav for mobile */
  .primary-nav .container {
    justify-content: space-between;
  }
  
  .nav-right .theme-toggle,
  .nav-right .search-btn {
    display: none; /* Hide on mobile, available in bottom bar */
  }
  
  /* Logo adjustments */
  .logo {
    font-size: 1.1rem;
  }
  
  .logo-icon {
    width: 32px;
    height: 32px;
  }
  
  /* AdSense mobile adjustments */
  .ad-container.ad-top-banner {
    margin: 5px 0;
    min-height: 60px;
  }
  
  .ad-placeholder {
    padding: 12px;
    font-size: 0.8rem;
    max-width: 320px;
  }
  
  /* Hero mobile adjustments */
  .hero {
    min-height: 60vh;
    text-align: center;
  }
  
  .hero-content {
    max-width: 100%;
  }
  
  .hero-title {
    font-size: 2rem;
    margin-bottom: 16px;
  }
  
  .hero-subtitle {
    font-size: 0.95rem;
    margin-bottom: 24px;
  }
  
  .hero-cta {
    justify-content: center;
    margin-bottom: 32px;
  }
  
  .cta-button {
    padding: 12px 24px;
    font-size: 0.9rem;
    min-width: 120px;
  }
  
  .hero-stats {
    justify-content: center;
    gap: 20px;
  }
  
  .stat-number {
    font-size: 1.25rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
  
  /* Floating shapes adjustments */
  .shape-1 {
    width: 60px;
    height: 60px;
    top: 15%;
    right: 5%;
  }
  
  .shape-2 {
    width: 50px;
    height: 50px;
    top: 70%;
    right: 10%;
  }
  
  .shape-3 {
    width: 40px;
    height: 40px;
    top: 45%;
    right: 2%;
  }
}

/* Mobile Portrait */
@media (max-width: 480px) {
  :root {
    --container-padding: 8px;
  }
  
  /* Further mobile optimizations */
  .logo {
    font-size: 1rem;
  }
  
  .logo-text {
    display: none; /* Show only icon on very small screens */
  }
  
  .nav-right {
    gap: 4px;
  }
  
  .wishlist-btn,
  .cart-btn {
    padding: 6px;
  }
  
  /* Hero further adjustments */
  .hero {
    min-height: 50vh;
    padding: 20px 0;
  }
  
  .hero-title {
    font-size: 1.75rem;
    line-height: 1.3;
  }
  
  .hero-subtitle {
    font-size: 0.9rem;
    margin-bottom: 20px;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
  }
  
  .cta-button {
    width: 100%;
    max-width: 280px;
  }
  
  .hero-stats {
    gap: 16px;
  }
  
  .stat-item {
    flex: 1;
  }
  
  .stat-number {
    font-size: 1.1rem;
  }
  
  .stat-label {
    font-size: 0.75rem;
  }
  
  /* Mobile bar adjustments */
  .mobile-btn {
    padding: 6px 8px;
    min-width: 50px;
  }
  
  .mobile-btn span {
    font-size: 0.7rem;
  }
  
  .mobile-btn i {
    font-size: 1.1rem;
  }
  
  /* AdSense very small mobile */
  .ad-placeholder {
    padding: 8px;
    font-size: 0.75rem;
    max-width: 280px;
  }
  
  .ad-container.ad-top-banner {
    min-height: 50px;
  }
}

/* RTL Responsive Adjustments */
[dir="rtl"] .utility-right {
  flex-direction: row-reverse;
}

[dir="rtl"] .nav-right {
  flex-direction: row-reverse;
}

[dir="rtl"] .hero-content {
  text-align: right;
}

[dir="rtl"] .hero-cta {
  justify-content: flex-end;
}

[dir="rtl"] .hero-stats {
  justify-content: flex-end;
}

[dir="rtl"] .mega-menu {
  right: 0;
  left: auto;
  transform-origin: right top;
}

[dir="rtl"] .currency-dropdown {
  right: auto;
  left: 0;
}

[dir="rtl"] .category-section a:hover {
  padding-right: 12px;
  padding-left: 8px;
}

/* Mobile RTL adjustments */
@media (max-width: 768px) {
  [dir="rtl"] .hero-content {
    text-align: center;
  }
  
  [dir="rtl"] .hero-cta {
    justify-content: center;
  }
  
  [dir="rtl"] .hero-stats {
    justify-content: center;
  }
}

/* High DPI / Retina Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-icon,
  .flag-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
  .floating-shapes {
    display: none;
  }
  
  .hero-title {
    background: var(--text-primary);
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
  }
  
  .cta-button.primary:hover {
    transform: none;
  }
  
  .resource-card:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .utility-bar,
  .primary-nav,
  .mobile-bar,
  .ad-container,
  .floating-shapes {
    display: none !important;
  }
  
  .hero {
    min-height: auto;
    padding: 20px 0;
  }
  
  .hero-background {
    display: none;
  }
  
  body {
    background: white !important;
    color: black !important;
    padding-bottom: 0;
  }
  
  .hero-title {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }
}

/* Dark Mode Specific Responsive Adjustments */
@media (max-width: 768px) {
  [data-theme="light"] .mobile-bar {
    background: rgba(248, 249, 250, 0.95);
  }
  
  [data-theme="light"] .primary-nav.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
  }
}

/* Accessibility Enhancements for Mobile */
@media (max-width: 768px) {
  .mobile-btn:focus {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
  }
  
  .cta-button:focus {
    outline: 2px solid var(--accent);
    outline-offset: 4px;
  }
}

/* New Sections Responsive Styles */

/* Tablet Adjustments for New Sections */
@media (max-width: 1024px) {
  .section-title {
    font-size: 2.2rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
  }

  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 24px;
  }

  .newsletter-content {
    gap: 30px;
  }

  .footer-content {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 30px;
  }
}

/* Mobile Adjustments for New Sections */
@media (max-width: 768px) {
  .featured-products-section,
  .categories-section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .product-card {
    max-width: 400px;
    margin: 0 auto;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .category-card {
    max-width: 350px;
    margin: 0 auto;
  }

  .newsletter-section {
    padding: 40px 0;
  }

  .newsletter-content {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
  }

  .newsletter-text h2 {
    font-size: 1.6rem;
  }

  .subscribe-form {
    flex-direction: column;
    max-width: 100%;
  }

  .subscribe-form input {
    padding: 12px 16px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .footer-description {
    max-width: 100%;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .footer-links {
    justify-content: center;
  }

  /* Search overlay mobile adjustments */
  .search-overlay {
    padding-top: 60px;
  }

  .search-container {
    margin: 0 16px;
    padding: 24px;
  }

  .search-header {
    flex-direction: column;
    gap: 16px;
  }

  .search-close {
    align-self: flex-end;
    padding: 12px;
  }
}

/* Small Mobile Adjustments */
@media (max-width: 480px) {
  .section-title {
    font-size: 1.6rem;
  }

  .section-subtitle {
    font-size: 0.95rem;
  }

  .product-info {
    padding: 16px;
  }

  .product-title {
    font-size: 1rem;
  }

  .product-description {
    font-size: 0.85rem;
  }

  .product-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .category-card {
    padding: 24px 16px;
  }

  .category-icon {
    width: 50px;
    height: 50px;
  }

  .category-card h3 {
    font-size: 1.1rem;
  }

  .newsletter-text h2 {
    font-size: 1.4rem;
  }

  .newsletter-text p {
    font-size: 1rem;
  }

  .social-links a {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .footer-links {
    flex-direction: column;
    gap: 12px;
  }

  .suggestion-tags {
    justify-content: center;
  }
}

/* Product Card Hover Adjustments for Touch Devices */
@media (hover: none) and (pointer: coarse) {
  .product-overlay {
    opacity: 1;
    background: rgba(0, 0, 0, 0.6);
  }

  .product-card:hover {
    transform: none;
  }

  .category-card:hover {
    transform: none;
  }
}

/* Newsletter Form Mobile Landscape */
@media (max-width: 768px) and (orientation: landscape) {
  .newsletter-content {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .subscribe-form {
    flex-direction: row;
  }
}

/* Landscape Phone Specific */
@media (max-width: 768px) and (orientation: landscape) {
  .hero {
    min-height: 70vh;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .mobile-bar {
    height: 60px;
  }

  :root {
    --mobile-bar-height: 60px;
  }

  .featured-products-section,
  .categories-section {
    padding: 50px 0;
  }
}
