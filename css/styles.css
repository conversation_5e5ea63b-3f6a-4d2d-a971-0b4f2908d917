/* Digistore3 - Main Styles */

/* CSS Variables for Theme System */
:root {
  /* Colors */
  --primary-bg: #181818;
  --utility-bg: #1a1a1a;
  --accent: #ff6f61;
  --accent-hover: #ff5a4a;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #888888;
  --border-color: #333333;
  --menu-backdrop: rgba(25, 25, 25, 0.85);
  --card-bg: #2a2a2a;
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  
  /* Spacing */
  --container-max-width: 1200px;
  --container-padding: 20px;
  --nav-height: 70px;
  --utility-height: 40px;
  --mobile-bar-height: 70px;
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  --font-heading: 'Poppins', var(--font-primary);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.25);
}

/* Light Theme Variables */
[data-theme="light"] {
  --primary-bg: #ffffff;
  --utility-bg: #f8f9fa;
  --text-primary: #1a1a1a;
  --text-secondary: #4a4a4a;
  --text-muted: #666666;
  --border-color: #e0e0e0;
  --menu-backdrop: rgba(255, 255, 255, 0.95);
  --card-bg: #ffffff;
}

/* RTL Support */
[dir="rtl"] {
  --text-align: right;
}

[dir="ltr"] {
  --text-align: left;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Container */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

/* Utility Bar */
.utility-bar {
  background-color: var(--utility-bg);
  height: var(--utility-height);
  font-size: 0.875rem;
  border-bottom: 1px solid var(--border-color);
}

.utility-bar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.utility-left .contact-info {
  color: var(--text-muted);
}

.utility-left .contact-info i {
  margin: 0 8px 0 16px;
}

.utility-left .contact-info i:first-child {
  margin-left: 0;
}

.utility-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Language Toggle */
.language-toggle {
  display: flex;
  gap: 4px;
}

.lang-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 0.75rem;
}

.lang-btn.active,
.lang-btn:hover {
  background-color: var(--accent);
  color: white;
  border-color: var(--accent);
}

/* Currency Selector */
.currency-selector {
  position: relative;
}

.currency-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  transition: var(--transition-fast);
}

.currency-btn:hover {
  border-color: var(--accent);
  color: var(--text-primary);
}

.flag-icon {
  width: 16px;
  height: 12px;
  object-fit: cover;
  border-radius: 2px;
}

.currency-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 0;
  min-width: 100px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition-fast);
  z-index: 1000;
  box-shadow: var(--shadow-md);
}

.currency-selector:hover .currency-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.currency-dropdown button {
  width: 100%;
  background: none;
  border: none;
  padding: 8px 12px;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: var(--transition-fast);
}

.currency-dropdown button:hover {
  background-color: var(--accent);
  color: white;
}

/* Account Links */
.account-links {
  display: flex;
  gap: 12px;
}

.auth-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: var(--transition-fast);
}

.auth-link:hover {
  color: var(--accent);
}

.auth-link.signup {
  background-color: var(--accent);
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
}

.auth-link.signup:hover {
  background-color: var(--accent-hover);
  color: white;
}

/* AdSense Containers */
.ad-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  min-height: 90px;
}

.ad-container.ad-top-banner {
  background-color: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid var(--border-color);
}

.ad-container.ad-mega-menu {
  margin: 15px 0 0 0;
  min-height: 60px;
}

.ad-placeholder {
  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
  color: #666;
  padding: 20px;
  border-radius: 6px;
  text-align: center;
  font-size: 0.875rem;
  border: 2px dashed #ccc;
  width: 100%;
  max-width: 728px;
}

.ad-placeholder.small {
  padding: 10px;
  font-size: 0.75rem;
  max-width: 300px;
  min-height: 40px;
}

/* Primary Navigation */
.primary-nav {
  background-color: var(--primary-bg);
  height: var(--nav-height);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: var(--transition-normal);
}

.primary-nav.scrolled {
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  background-color: rgba(24, 24, 24, 0.95);
}

.primary-nav .container {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: space-between;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.25rem;
}

.logo-icon {
  width: 40px;
  height: 40px;
}

.logo-text {
  font-family: var(--font-heading);
}

/* Main Menu */
.main-menu {
  display: flex;
  list-style: none;
  gap: 32px;
  align-items: center;
}

.menu-item {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  padding: 8px 0;
  position: relative;
  transition: var(--transition-fast);
}

.menu-item:hover,
.menu-item.active {
  color: var(--accent);
}

.menu-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent);
  transition: var(--transition-fast);
}

.menu-item:hover::after,
.menu-item.active::after {
  width: 100%;
}

/* Navigation Right */
.nav-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.theme-toggle,
.search-btn,
.wishlist-btn,
.cart-btn {
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: var(--transition-fast);
  position: relative;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover,
.search-btn:hover,
.wishlist-btn:hover,
.cart-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--accent);
}

.theme-toggle {
  width: 40px;
  height: 40px;
  position: relative;
  overflow: hidden;
}

.theme-toggle i {
  position: absolute;
  transition: var(--transition-normal);
}

.theme-toggle .icon-sun {
  opacity: 1;
  transform: rotate(0deg);
}

.theme-toggle .icon-moon {
  opacity: 0;
  transform: rotate(180deg);
}

[data-theme="light"] .theme-toggle .icon-sun {
  opacity: 0;
  transform: rotate(180deg);
}

[data-theme="light"] .theme-toggle .icon-moon {
  opacity: 1;
  transform: rotate(0deg);
}

.badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background-color: var(--accent);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

/* Icons (Using CSS for basic icons) */
.icon-email::before { content: "✉"; }
.icon-phone::before { content: "📞"; }
.icon-sun::before { content: "☀"; }
.icon-moon::before { content: "🌙"; }
.icon-search::before { content: "🔍"; }
.icon-heart::before { content: "♡"; }
.icon-cart::before { content: "🛒"; }
.icon-home::before { content: "🏠"; }
.icon-grid::before { content: "⊞"; }
.icon-tutorials::before { content: "📚"; }
.icon-support::before { content: "🎧"; }
.icon-community::before { content: "👥"; }

/* Mobile Bar */
.mobile-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--mobile-bar-height);
  background: var(--menu-backdrop);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--border-color);
  display: none;
  justify-content: space-around;
  align-items: center;
  z-index: 1000;
  padding: 8px 0;
}

.mobile-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: var(--transition-fast);
  position: relative;
  font-size: 0.75rem;
  min-width: 60px;
}

.mobile-btn.active,
.mobile-btn:hover {
  color: var(--accent);
  background-color: rgba(255, 111, 97, 0.1);
}

.mobile-btn.active::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: var(--accent);
  border-radius: 2px;
}

.mobile-btn i {
  font-size: 1.2rem;
}

.mobile-btn .badge {
  top: 4px;
  right: 8px;
}

/* Mega Menu Styles */
.has-mega {
  position: relative;
}

.mega-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--menu-backdrop);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 30px;
  min-width: 800px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: var(--transition-normal);
  z-index: 999;
  box-shadow: var(--shadow-xl);
}

.has-mega:hover .mega-menu {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* Categories Mega Menu */
.categories-mega .mega-content {
  display: grid;
  grid-template-columns: 1fr 200px;
  gap: 30px;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.category-section h3 {
  color: var(--accent);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  font-family: var(--font-heading);
}

.category-section ul {
  list-style: none;
}

.category-section li {
  margin-bottom: 8px;
}

.category-section a {
  color: var(--text-primary);
  text-decoration: none;
  padding: 6px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition-fast);
  border-radius: 4px;
  padding-left: 8px;
}

.category-section a:hover {
  color: var(--accent);
  background-color: rgba(255, 111, 97, 0.1);
  padding-left: 12px;
}

.hot-badge {
  background: linear-gradient(45deg, var(--accent), #ff8a65);
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.featured-products h4 {
  color: var(--text-primary);
  font-size: 1rem;
  margin-bottom: 16px;
  font-weight: 600;
}

.featured-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.featured-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  transition: var(--transition-fast);
  cursor: pointer;
}

.featured-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.featured-item img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 6px;
  background: var(--card-bg);
}

.featured-item .price {
  color: var(--accent);
  font-weight: 600;
  font-size: 0.9rem;
}

/* Resources Mega Menu */
.resources-mega .mega-content {
  max-width: 600px;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.resource-card {
  background: var(--card-bg);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  transition: var(--transition-normal);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.resource-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--card-bg), rgba(255, 111, 97, 0.1));
}

.card-header {
  margin-bottom: 12px;
}

.card-header i {
  font-size: 2rem;
  color: var(--accent);
  margin-bottom: 8px;
  display: block;
}

.card-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0;
}

.resource-card p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 16px;
  line-height: 1.5;
}

.card-link {
  color: var(--accent);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition-fast);
}

.card-link:hover {
  color: var(--accent-hover);
}

.mega-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.view-all-link {
  color: var(--accent);
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border: 1px solid var(--accent);
  border-radius: 6px;
  transition: var(--transition-fast);
}

.view-all-link:hover {
  background-color: var(--accent);
  color: white;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, var(--primary-bg) 0%, #2a2a2a 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, transparent 70%);
}

.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.shape {
  position: absolute;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  top: 20%;
  right: 10%;
  width: 100px;
  height: 100px;
  animation-delay: 0s;
}

.shape-2 {
  top: 60%;
  right: 20%;
  width: 80px;
  height: 80px;
  animation-delay: 2s;
}

.shape-3 {
  top: 40%;
  right: 5%;
  width: 60px;
  height: 60px;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero .container {
  position: relative;
  z-index: 2;
}

.hero-content {
  max-width: 600px;
}

.hero-title {
  font-family: var(--font-heading);
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 20px;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--text-primary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 32px;
  line-height: 1.6;
}

.hero-cta {
  display: flex;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.cta-button {
  padding: 14px 28px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(135deg, var(--accent), var(--accent-hover));
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(255, 111, 97, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 111, 97, 0.4);
}

.cta-button.secondary {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.cta-button.secondary:hover {
  border-color: var(--accent);
  color: var(--accent);
  background: rgba(255, 111, 97, 0.1);
}

.hero-stats {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent);
  font-family: var(--font-heading);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-family: var(--font-heading);
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
  background: linear-gradient(135deg, var(--text-primary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.section-cta {
  text-align: center;
  margin-top: 40px;
}

/* Featured Products Section */
.featured-products-section {
  padding: 80px 0;
  background: var(--primary-bg);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.product-card {
  background: var(--card-bg);
  border-radius: 12px;
  overflow: hidden;
  transition: var(--transition-normal);
  border: 1px solid var(--border-color);
  position: relative;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent), var(--accent-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: var(--transition-normal);
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-view-btn,
.add-to-cart-btn {
  background: var(--accent);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.quick-view-btn:hover,
.add-to-cart-btn:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
}

.add-to-cart-btn {
  background: transparent;
  border: 2px solid white;
}

.add-to-cart-btn:hover {
  background: white;
  color: var(--accent);
}

.product-info {
  padding: 20px;
}

.product-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  line-height: 1.4;
}

.product-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 16px;
  line-height: 1.5;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--accent);
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stars {
  color: #ffc107;
  font-size: 0.9rem;
}

.rating-count {
  color: var(--text-muted);
  font-size: 0.8rem;
}

/* Categories Section */
.categories-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary-bg) 0%, rgba(255, 111, 97, 0.05) 100%);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.category-card {
  background: var(--card-bg);
  padding: 30px 20px;
  border-radius: 12px;
  text-align: center;
  transition: var(--transition-normal);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--card-bg), rgba(255, 111, 97, 0.1));
}

.category-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 111, 97, 0.1);
  border-radius: 12px;
  transition: var(--transition-normal);
}

.category-card:hover .category-icon {
  background: var(--accent);
  transform: scale(1.1);
}

.category-card:hover .category-icon svg {
  fill: white;
}

.category-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.category-card p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 16px;
  line-height: 1.5;
}

.item-count {
  color: var(--accent);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Newsletter Section */
.newsletter-section {
  padding: 60px 0;
  background: var(--accent);
  color: white;
}

.newsletter-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.newsletter-text h2 {
  font-family: var(--font-heading);
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 16px;
}

.newsletter-text p {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

.subscribe-form {
  display: flex;
  gap: 12px;
  max-width: 400px;
}

.subscribe-form input {
  flex: 1;
  padding: 14px 16px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  color: var(--primary-bg);
}

.subscribe-form input::placeholder {
  color: #999;
}

.subscribe-form .cta-button {
  background: var(--primary-bg);
  color: white;
  white-space: nowrap;
}

.subscribe-form .cta-button:hover {
  background: #0a0a0a;
}

/* Footer Styles */
.main-footer {
  background: var(--utility-bg);
  border-top: 1px solid var(--border-color);
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 12px;
}

.footer-section ul li a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
}

.footer-section ul li a:hover {
  color: var(--accent);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.footer-logo .logo-icon {
  width: 32px;
  height: 32px;
}

.footer-logo .logo-text {
  font-family: var(--font-heading);
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
  max-width: 300px;
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 1.2rem;
  transition: var(--transition-fast);
}

.social-links a:hover {
  background: var(--accent);
  border-color: var(--accent);
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-bottom p {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.footer-links {
  display: flex;
  gap: 24px;
}

.footer-links a {
  color: var(--text-muted);
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition-fast);
}

.footer-links a:hover {
  color: var(--accent);
}

/* Search Overlay Styles */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 2000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100px;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.search-overlay.active {
  opacity: 1;
  visibility: visible;
}

.search-container {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 30px;
  width: 100%;
  max-width: 600px;
  margin: 0 20px;
  border: 1px solid var(--border-color);
}

.search-header {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;
}

.search-input {
  flex: 1;
  padding: 16px 20px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--primary-bg);
  color: var(--text-primary);
  font-size: 1.1rem;
  transition: var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--accent);
}

.search-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 16px;
  border-radius: 8px;
  transition: var(--transition-fast);
}

.search-close:hover {
  background: var(--accent);
  color: white;
}

.search-suggestions h4 {
  color: var(--text-primary);
  font-size: 1rem;
  margin-bottom: 16px;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background: var(--accent);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition-fast);
}

.tag:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
}

/* Additional AdSense Styles */
.ad-section-break {
  margin: 40px 0;
  background: rgba(255, 255, 255, 0.01);
}

.ad-footer-top {
  margin: 0 0 40px 0;
  padding: 20px 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

/* Show mobile bar on small screens */
@media (max-width: 768px) {
  .mobile-bar {
    display: flex;
  }

  body {
    padding-bottom: var(--mobile-bar-height);
  }
}
