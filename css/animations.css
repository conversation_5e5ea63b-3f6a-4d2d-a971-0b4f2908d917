/* Digistore3 - Animations & Transitions */

/* Keyframe Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(255, 111, 97, 0.3);
  }
  50% {
    box-shadow: 0 8px 25px rgba(255, 111, 97, 0.5);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Page Load Animations */
.hero-title {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-subtitle {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-cta {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.hero-stats {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

/* Navigation Animations */
.primary-nav {
  animation: fadeInDown 0.6s ease-out both;
}

.mobile-bar {
  animation: slideInFromBottom 0.6s ease-out both;
}

/* Mega Menu Animations */
.mega-menu {
  animation: scaleIn 0.3s ease-out both;
}

.category-section {
  animation: fadeInLeft 0.4s ease-out both;
}

.category-section:nth-child(2) {
  animation-delay: 0.1s;
}

.category-section:nth-child(3) {
  animation-delay: 0.2s;
}

.category-section:nth-child(4) {
  animation-delay: 0.3s;
}

.featured-products {
  animation: fadeInRight 0.4s ease-out 0.2s both;
}

.resource-card {
  animation: fadeInUp 0.4s ease-out both;
}

.resource-card:nth-child(2) {
  animation-delay: 0.1s;
}

.resource-card:nth-child(3) {
  animation-delay: 0.2s;
}

/* Button Hover Animations */
.cta-button.primary {
  animation: glow 2s ease-in-out infinite;
}

.cta-button:hover {
  animation: pulse 0.3s ease-in-out;
}

/* Badge Animations */
.badge {
  animation: bounce 1s ease-in-out infinite;
}

.hot-badge {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Loading Shimmer Effect */
.shimmer {
  background: linear-gradient(
    90deg,
    var(--card-bg) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    var(--card-bg) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Scroll-triggered Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Stagger Animation for Lists */
.stagger-animation > * {
  animation: fadeInUp 0.6s ease-out both;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(255, 111, 97, 0.3);
}

/* Icon Animations */
.icon-spin {
  animation: rotate 1s linear infinite;
}

.icon-bounce {
  animation: bounce 1s ease-in-out infinite;
}

/* Theme Toggle Animation */
.theme-toggle i {
  transition: all 0.3s ease;
}

/* Mobile Bar Slide Animation */
.mobile-bar.slide-up {
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.mobile-bar.slide-down {
  transform: translateY(0);
  transition: transform 0.3s ease;
}

/* Search Animation */
.search-expand {
  width: 0;
  opacity: 0;
  transition: all 0.3s ease;
}

.search-expand.active {
  width: 200px;
  opacity: 1;
}

/* Cart/Wishlist Badge Animation */
.badge-update {
  animation: pulse 0.5s ease-in-out;
}

/* Mega Menu Exit Animation */
.mega-menu.closing {
  animation: scaleOut 0.2s ease-in both;
}

@keyframes scaleOut {
  from {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) scale(0.95);
    opacity: 0;
  }
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

/* Intersection Observer Animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease-out;
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s ease-out;
}

.fade-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s ease-out;
}

.fade-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Reduced Motion Overrides */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .floating-shapes .shape {
    animation: none;
  }
  
  .cta-button.primary {
    animation: none;
  }
  
  .badge {
    animation: none;
  }
  
  .hot-badge {
    animation: none;
  }
}
