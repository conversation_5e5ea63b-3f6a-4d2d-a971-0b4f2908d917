# Digistore3 - Modern Digital Products Store Frontend

A modern, responsive HTML/CSS/JavaScript front-end for a digital products store, featuring a playful yet professional design suitable for both kids and professionals.

## 🚀 Features

### 🧭 Navigation System
- **Dual-Bar Navigation**: Top utility bar with contact info, language/currency switchers, and account links
- **Primary Navigation**: Sticky navigation with logo, menu items, and action buttons
- **Mobile Bottom Bar**: iOS-style fixed bottom navigation for mobile devices
- **Mega Menus**: Feature-rich dropdown menus with categories and featured products

### 🎨 Design Elements
- **Modern Color Scheme**: Dark theme with accent color (#ff6f61) and light theme support
- **Responsive Design**: Fully responsive across all device sizes
- **RTL Support**: Complete right-to-left language support for Arabic
- **Smooth Animations**: CSS animations with reduced motion support
- **Theme Toggle**: Dark/light mode switching with localStorage persistence

### 🌟 Interactive Components
- **Hero Section**: Full-bleed background with animated floating shapes
- **Product Cards**: Hover effects with overlay actions
- **Category Cards**: Interactive category browsing
- **Search Overlay**: Full-screen search with suggestions
- **Newsletter Signup**: Email subscription form
- **Currency Converter**: Multi-currency support (USD, EUR, SAR)

### 📱 Mobile Optimization
- **Mobile-First Design**: Optimized for mobile devices
- **Touch-Friendly**: Large touch targets and gesture support
- **Auto-Hide Navigation**: Smart mobile bar behavior on scroll
- **Responsive Images**: Optimized image loading and display

### 💰 AdSense Integration
Strategic ad placements throughout the site:
- Top banner (below utility bar)
- Mega menu subtle banners
- Section breaks between content
- Footer area
- All ad containers are responsive and mobile-friendly

## 📁 File Structure

```
Digistore3/
├── index.html              # Main HTML file
├── css/
│   ├── styles.css          # Main styles and components
│   ├── responsive.css      # Responsive breakpoints
│   └── animations.css      # Animation definitions
├── js/
│   ├── main.js            # Core functionality
│   └── animations.js      # Advanced animations
├── assets/
│   ├── favicon.svg        # Site favicon
│   ├── flags/             # Currency flag icons
│   │   ├── us.svg
│   │   ├── eu.svg
│   │   └── sa.svg
│   └── products/          # Product placeholder images
│       ├── featured-1.jpg
│       └── featured-2.jpg
└── README.md              # This file
```

## 🛠️ Setup & Installation

1. **Clone or download** the project files
2. **Serve the files** using any web server:
   ```bash
   # Using Python
   python3 -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
3. **Open browser** and navigate to `http://localhost:8000`

## 🎯 AdSense Integration Guide

### Ad Placement Locations

1. **Top Banner** (`#ad-top-banner`)
   - Below utility bar, above main navigation
   - Recommended size: 728x90 (Leaderboard)

2. **Mega Menu Ads** (`#ad-mega-menu`)
   - Subtle banners within dropdown menus
   - Recommended size: 300x50 (Mobile Banner)

3. **Section Breaks** (`#ad-hero-break`, `#ad-products-break`)
   - Between major content sections
   - Recommended size: 728x90 or 320x100 (mobile)

4. **Footer Ad** (`#ad-footer-top`)
   - Above footer content
   - Recommended size: 728x90 or responsive

### Implementation Steps

1. **Get AdSense approval** for your domain
2. **Replace placeholder comments** in `index.html`:
   ```html
   <!-- Replace this comment -->
   <!-- <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX" crossorigin="anonymous"></script> -->
   
   <!-- With your actual AdSense code -->
   <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-YOUR-ID" crossorigin="anonymous"></script>
   ```

3. **Replace ad placeholders** with AdSense ad units:
   ```html
   <!-- Replace -->
   <div class="ad-placeholder">Advertisement</div>
   
   <!-- With -->
   <ins class="adsbygoogle"
        style="display:block"
        data-ad-client="ca-pub-YOUR-ID"
        data-ad-slot="YOUR-SLOT-ID"
        data-ad-format="auto"></ins>
   <script>(adsbygoogle = window.adsbygoogle || []).push({});</script>
   ```

## 🌍 Internationalization

### Language Support
- **English (EN)**: Default language
- **Arabic (AR)**: RTL support with proper text direction
- **Extensible**: Easy to add more languages

### Currency Support
- **USD**: US Dollar (default)
- **EUR**: Euro
- **SAR**: Saudi Riyal
- **Auto-conversion**: Prices update based on selected currency

## 🎨 Customization

### Colors
Edit CSS variables in `css/styles.css`:
```css
:root {
  --accent: #ff6f61;        /* Primary accent color */
  --accent-hover: #ff5a4a;  /* Hover state */
  --primary-bg: #181818;    /* Dark background */
  /* ... more variables */
}
```

### Typography
```css
:root {
  --font-primary: 'Inter', sans-serif;
  --font-heading: 'Poppins', sans-serif;
}
```

### Animations
Control animations in `css/animations.css` or disable with:
```css
@media (prefers-reduced-motion: reduce) {
  /* Animations are automatically disabled */
}
```

## 📱 Browser Support

- **Modern Browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile**: iOS Safari 13+, Chrome Mobile 80+
- **Features**: CSS Grid, Flexbox, CSS Variables, Intersection Observer

## 🚀 Performance Features

- **Lazy Loading**: Images load as they enter viewport
- **CSS Optimization**: Efficient selectors and minimal repaints
- **JavaScript**: Vanilla JS with no external dependencies
- **Responsive Images**: Optimized for different screen sizes
- **Preloading**: Critical resources preloaded for faster initial load

## 🔧 Development

### Adding New Sections
1. Add HTML structure to `index.html`
2. Add styles to `css/styles.css`
3. Add responsive styles to `css/responsive.css`
4. Add animations to `css/animations.css` if needed

### JavaScript API
Access functionality via the global `DigiStore3` object:
```javascript
// Update currency
DigiStore3.updateCurrency('eur');

// Toggle search
DigiStore3.toggleSearch();

// Update badge counts
DigiStore3.updateBadge('.cart-btn .badge', 5);
```

## 📄 License

This project is provided as-is for educational and commercial use. Customize as needed for your specific requirements.

## 🤝 Support

For questions or customization requests, refer to the code comments and documentation within the files.

---

**Built with ❤️ for modern digital commerce**
